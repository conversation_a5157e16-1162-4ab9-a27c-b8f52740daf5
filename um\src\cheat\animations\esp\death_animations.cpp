#include "pch.h"
#include "death_animations.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../features/visuals/visuals.hpp"

// Static member definitions
ImVec4 DeathAnimations::originalBoxColor;
ImVec4 DeathAnimations::originalSkeletonColor;
ImVec4 DeathAnimations::originalSnaplineColor;
ImVec4 DeathAnimations::originalInfoColor;
ImVec4 DeathAnimations::originalHealthColor;
ImVec4 DeathAnimations::originalArmorColor;
bool DeathAnimations::originalHealthGlow;
bool DeathAnimations::originalArmorGlow;

// ===========================
// MAIN RENDERING FUNCTION
// ===========================

void DeathAnimations::RenderAll(const view_matrix_t& viewMatrix, int localTeam) {
    if (!globals::Esp::Death::enabled) return;

    // Update animations (cleanup expired ones)
    AnimationManager::Update();

    // Get all active death animations and render them
    auto& deathAnimations = AnimationManager::deathAnimations;
    for (auto& [entityId, deathDataPtr] : deathAnimations) {
        if (!deathDataPtr) continue;

        float fadeAlpha = AnimationManager::GetDeathFadeAlpha(entityId);
        if (fadeAlpha <= 0.0f) continue;

        // Team check
        const bool isDeadPlayerEnemy = (deathDataPtr->team != localTeam);
        if (!isDeadPlayerEnemy && globals::Esp::ignoreTeammates) continue;

        RenderDeathPlayer(viewMatrix, entityId, *deathDataPtr, fadeAlpha);
    }
}

// ===========================
// REGISTRATION HELPER
// ===========================

void DeathAnimations::RegisterPlayerDeath(int entityId, const Vector& position, const Vector& eyePosition,
                                         uint64_t boneArray, const std::string& playerName, uint32_t playerFlags,
                                         uint16_t weaponIndex, int health, int armor, int team, bool wasSpotted) {
    // Use the centralized animation manager
    AnimationManager::RegisterDeathFade(entityId, position, eyePosition, boneArray, playerName,
                                       playerFlags, weaponIndex, health, armor, team, wasSpotted,
                                       globals::Esp::Death::duration);
}

// ===========================
// RENDERING FUNCTIONS
// ===========================

void DeathAnimations::RenderDeathPlayer(const view_matrix_t& viewMatrix, int entityId,
                                       const DeathAnimationData& deathData, float fadeAlpha) {
    // Convert world position to screen coordinates
    Vector playerWorldHeadPos = deathData.worldPosition;
    playerWorldHeadPos.z += 75.0f; // Approximate head height
    Vector playerWorldFeetPos = deathData.worldPosition;

    Vector currentScreenHeadPos, currentScreenFeetPos;
    if (!Vector::world_to_screen(viewMatrix, playerWorldHeadPos, currentScreenHeadPos) ||
        !Vector::world_to_screen(viewMatrix, playerWorldFeetPos, currentScreenFeetPos)) {
        return; // Skip if position is not visible
    }

    // Setup death color with fade alpha
    ImVec4 deathColor = globals::Esp::Death::Color;
    deathColor.w *= fadeAlpha;

    // Store original settings and apply death settings
    ApplyDeathRenderingSettings(deathColor);

    // Render death animation elements
    const float boxHeight = currentScreenFeetPos.y - currentScreenHeadPos.y;
    const float boxHalfWidth = boxHeight * 0.25f;

    if (globals::Esp::Box::enabled && globals::Esp::Death::Box::enabled) {
        RenderDeathBox(viewMatrix, currentScreenHeadPos, currentScreenFeetPos);
    }

    if (globals::Esp::Skeleton::enabled && globals::Esp::Death::Skeleton::enabled) {
        RenderDeathSkeleton(viewMatrix, deathData.frozenBones);

        if (globals::Esp::Skeleton::Head::enabled) {
            RenderDeathSkeletonHead(viewMatrix, deathData.frozenBones);
        }
    }

    if (globals::Esp::Info::enabled && globals::Esp::Death::Info::enabled) {
        RenderDeathInfo(viewMatrix, currentScreenHeadPos, currentScreenFeetPos,
                       deathData.playerName, deathData.playerFlags, deathData.weaponIndex);
    }

    if (globals::Esp::Snapline::enabled && globals::Esp::Death::Snapline::enabled) {
        RenderDeathSnapline(viewMatrix, currentScreenHeadPos, currentScreenFeetPos);
    }

    if (globals::Esp::Health::Bar::enabled && globals::Esp::Death::Health::enabled) {
        RenderDeathHealthBar(viewMatrix, currentScreenHeadPos, boxHeight, boxHalfWidth, deathData.health, entityId);
    }

    if (globals::Esp::Armor::Bar::enabled && globals::Esp::Death::Armor::enabled) {
        RenderDeathArmorBar(viewMatrix, currentScreenHeadPos, boxHeight, boxHalfWidth, deathData.armor, entityId);
    }

    // Restore original settings
    RestoreOriginalRenderingSettings();
}

void DeathAnimations::ApplyDeathRenderingSettings(const ImVec4& deathColor) {
    // Store original colors
    originalBoxColor = globals::Esp::Box::Spotted::drawingColor;
    originalSkeletonColor = globals::Esp::Skeleton::Spotted::drawingColor;
    originalSnaplineColor = globals::Esp::Snapline::Spotted::drawingColor;
    originalInfoColor = globals::Esp::Info::Spotted::drawingColor;
    originalHealthColor = globals::Esp::Health::Bar::Color;
    originalArmorColor = globals::Esp::Armor::Bar::Color;
    originalHealthGlow = globals::Esp::Health::Bar::Glow::enabled;
    originalArmorGlow = globals::Esp::Armor::Bar::Glow::enabled;

    // Apply death color to all elements
    globals::Esp::Box::Spotted::drawingColor = deathColor;
    globals::Esp::Skeleton::Spotted::drawingColor = deathColor;
    globals::Esp::Snapline::Spotted::drawingColor = deathColor;
    globals::Esp::Info::Spotted::drawingColor = deathColor;
    globals::Esp::Health::Bar::Color = deathColor;
    globals::Esp::Armor::Bar::Color = deathColor;

    // Disable glow effects for death animations
    globals::Esp::Health::Bar::Glow::enabled = false;
    globals::Esp::Armor::Bar::Glow::enabled = false;
}

void DeathAnimations::RestoreOriginalRenderingSettings() {
    // Restore original colors
    globals::Esp::Box::Spotted::drawingColor = originalBoxColor;
    globals::Esp::Skeleton::Spotted::drawingColor = originalSkeletonColor;
    globals::Esp::Snapline::Spotted::drawingColor = originalSnaplineColor;
    globals::Esp::Info::Spotted::drawingColor = originalInfoColor;
    globals::Esp::Health::Bar::Color = originalHealthColor;
    globals::Esp::Armor::Bar::Color = originalArmorColor;
    globals::Esp::Health::Bar::Glow::enabled = originalHealthGlow;
    globals::Esp::Armor::Bar::Glow::enabled = originalArmorGlow;
}

// ===========================
// ELEMENT RENDERING
// ===========================

void DeathAnimations::RenderDeathBox(const view_matrix_t& viewMatrix, const Vector& screenHead, const Vector& screenFeet) {
    // Temporarily store current ESP data
    auto oldESPData = g_CurrentESP;

    // Set death animation data
    g_CurrentESP.screenHead = screenHead;
    g_CurrentESP.screenFeet = screenFeet;
    g_CurrentESP.boxHeight = screenFeet.y - screenHead.y;
    g_CurrentESP.boxHalfWidth = g_CurrentESP.boxHeight * 0.25f;

    if (globals::Esp::Box::type == 0) {
        VISUALS::DrawPlayerBox();
    } else {
        VISUALS::DrawPlayerCorneredBox();
    }

    // Restore original ESP data
    g_CurrentESP = oldESPData;
}

void DeathAnimations::RenderDeathSkeleton(const view_matrix_t& viewMatrix, const std::unordered_map<int, Vector>& frozenBones) {
    // Render skeleton using frozen bone positions
    for (const auto& connection : bConnections) {
        auto bone1It = frozenBones.find(connection.bone1);
        auto bone2It = frozenBones.find(connection.bone2);

        if (bone1It != frozenBones.end() && bone2It != frozenBones.end()) {
            Vector worldPos1 = bone1It->second;
            Vector worldPos2 = bone2It->second;
            Vector screenPos1, screenPos2;
            if (Vector::world_to_screen(viewMatrix, worldPos1, screenPos1) &&
                Vector::world_to_screen(viewMatrix, worldPos2, screenPos2)) {

                const ImVec4 lineColor = globals::Esp::Skeleton::Spotted::drawingColor;
                const float lineThickness = 1.0f;
                Render::Line(screenPos1.x, screenPos1.y, screenPos2.x, screenPos2.y, lineColor, lineThickness);
            }
        }
    }
}

void DeathAnimations::RenderDeathSkeletonHead(const view_matrix_t& viewMatrix, const std::unordered_map<int, Vector>& frozenBones) {
    auto headIt = frozenBones.find(6);  // Head bone
    auto neckIt = frozenBones.find(5);  // Neck bone
    auto spineIt = frozenBones.find(4); // Spine bone

    if (headIt == frozenBones.end() || neckIt == frozenBones.end() || spineIt == frozenBones.end()) {
        return;
    }

    Vector worldHeadPos = headIt->second;
    Vector worldNeckPos = neckIt->second;
    Vector worldSpinePos = spineIt->second;

    Vector screenHead, screenNeck, screenSpine;
    if (Vector::world_to_screen(viewMatrix, worldHeadPos, screenHead) &&
        Vector::world_to_screen(viewMatrix, worldNeckPos, screenNeck) &&
        Vector::world_to_screen(viewMatrix, worldSpinePos, screenSpine)) {

        const float headCircleRadius = std::abs(screenNeck.y - screenSpine.y);
        const ImVec4 headCircleColor = globals::Esp::Skeleton::Spotted::drawingColor;
        const float circleLineThickness = 1.0f;

        Render::AACircle(screenHead.x, screenHead.y, headCircleRadius, headCircleColor, circleLineThickness);
    }
}

void DeathAnimations::RenderDeathInfo(const view_matrix_t& viewMatrix, const Vector& screenHead, const Vector& screenFeet,
                                     const std::string& playerName, uint32_t playerFlags, uint16_t weaponIndex) {
    // Temporarily store current ESP data
    auto oldESPData = g_CurrentESP;

    // Set death animation data
    g_CurrentESP.screenHead = screenHead;
    g_CurrentESP.screenFeet = screenFeet;
    g_CurrentESP.boxHeight = screenFeet.y - screenHead.y;
    g_CurrentESP.boxHalfWidth = g_CurrentESP.boxHeight * 0.25f;
    g_CurrentESP.playerName = playerName;
    g_CurrentESP.playerFlags = playerFlags;
    g_CurrentESP.itemDefinitionIndex = weaponIndex;

    VISUALS::DrawPlayerInfo();

    // Restore original ESP data
    g_CurrentESP = oldESPData;
}

void DeathAnimations::RenderDeathSnapline(const view_matrix_t& viewMatrix, const Vector& screenHead, const Vector& screenFeet) {
    // Temporarily store current ESP data
    auto oldESPData = g_CurrentESP;

    // Set death animation data
    g_CurrentESP.screenHead = screenHead;
    g_CurrentESP.screenFeet = screenFeet;

    VISUALS::DrawPlayerSnapline();

    // Restore original ESP data
    g_CurrentESP = oldESPData;
}

void DeathAnimations::RenderDeathHealthBar(const view_matrix_t& viewMatrix, const Vector& screenHead,
                                          float boxHeight, float boxHalfWidth, int health, int entityId) {
    // Temporarily store current ESP data
    auto oldESPData = g_CurrentESP;

    // Set death animation data
    g_CurrentESP.screenHead = screenHead;
    g_CurrentESP.boxHeight = boxHeight;
    g_CurrentESP.boxHalfWidth = boxHalfWidth;
    g_CurrentESP.health = health;
    g_CurrentESP.entityId = entityId;

    VISUALS::DrawPlayerHealth();

    // Restore original ESP data
    g_CurrentESP = oldESPData;
}

void DeathAnimations::RenderDeathArmorBar(const view_matrix_t& viewMatrix, const Vector& screenHead,
                                         float boxHeight, float boxHalfWidth, int armor, int entityId) {
    // Temporarily store current ESP data
    auto oldESPData = g_CurrentESP;

    // Set death animation data
    g_CurrentESP.screenHead = screenHead;
    g_CurrentESP.boxHeight = boxHeight;
    g_CurrentESP.boxHalfWidth = boxHalfWidth;
    g_CurrentESP.armor = armor;
    g_CurrentESP.entityId = entityId;

    VISUALS::DrawPlayerArmor();

    // Restore original ESP data
    g_CurrentESP = oldESPData;
}
