#include "pch.h" // Make sure this includes <algorithm> for std::min/max or undefines macros
#include "visuals.hpp"
#include "../../../ItemIndex.hpp"
#include "../../globals.hpp"
#include "../../../utils/utils.hpp" // For RemoveSuffix and HorizontalCircle
#include "../../gamedata.hpp" // GameData convenience wrapper system
#include "../../../window/window.hpp" // For espfont and gunIcons

#define _USE_MATH_DEFINES
#include <algorithm> // For std::min and std::max
#include <cmath>     // For M_PI, cosf, sinf (ensure _USE_MATH_DEFINES is defined if M_PI is missing)
#include <vector>
#include <set>

using namespace globals;



void VISUALS::RenderESP( HANDLE driverHandle, const Reader& reader ) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  ImGui::GetStyle().AntiAliasedLines = false;
  ImGui::GetStyle().AntiAliasedFill = false;
  ImGui::GetStyle().AntiAliasedLinesUseTex = false;

  if ( DarkMode::enabled || DarkMode::enabled && overlayVisible ) {
    Darkmode();
  }

  // Render aimbot circle 
  if (globals::Legitbot::enabled) { 
    DrawAimbotCircle();
  }

  // CLEAN: Use GameData convenience wrapper system
  int localTeam = GameData::getLocalTeam();

  // Setup global ESP data
  g_CurrentESP.driverHandle = driverHandle;
  g_CurrentESP.viewMatrix = GameData::getViewMatrix();

  // Process death detection
  ProcessDeathDetection();

  for ( const auto& player : playerList ) {
    // Check if player is enemy or teammate
    const bool isCurrentPlayerEnemy = (player.team != localTeam);

    // Skip teammates if "Ignore Teammates" is enabled
    if (!isCurrentPlayerEnemy && Esp::ignoreTeammates) continue;

    // Set spotted drawing colors based on player spotted state
    bool spotted = player.PlayerSpotted;
    Esp::Box::Spotted::drawingColor      = ( spotted && Esp::Box::Spotted::enabled ) ? Esp::Box::Spotted::Color : Esp::Box::Color;
    Esp::Skeleton::Spotted::drawingColor = ( spotted && Esp::Skeleton::Spotted::enabled ) ? Esp::Skeleton::Spotted::Color : Esp::Skeleton::Color;
    Esp::Snapline::Spotted::drawingColor = ( spotted && Esp::Snapline::Spotted::enabled ) ? Esp::Snapline::Spotted::Color : Esp::Snapline::Color;
    Esp::Info::Spotted::drawingColor     = ( spotted && Esp::Info::Spotted::enabled ) ? Esp::Info::Spotted::Color : Esp::Info::Color;
    Esp::Viewline::Spotted::drawingColor = ( spotted && Esp::Viewline::Spotted::enabled ) ? Esp::Viewline::Spotted::Color : Esp::Viewline::Color;

    // CLEAN: Use GameData for player position
    Vector playerWorldOrigin = GameData::getPlayerPosition(player.pCSPlayerPawn);
    // Note: 0xCB0 offset might need to be added to GameData in the future
    Vector playerViewOffset = driver::read_memory<Vector>(driverHandle, player.pCSPlayerPawn + 0xCB0);
    Vector playerWorldHeadPos = playerWorldOrigin;
    playerWorldHeadPos.z += playerViewOffset.z + 9;

    Vector screenHeadPos, screenFeetPos;
    if ( Vector::world_to_screen( g_CurrentESP.viewMatrix, playerWorldHeadPos, screenHeadPos ) &&
      Vector::world_to_screen( g_CurrentESP.viewMatrix, playerWorldOrigin, screenFeetPos ) ) {

      // Fill global ESP data for current player
      g_CurrentESP.screenHead = screenHeadPos;
      g_CurrentESP.screenFeet = screenFeetPos;
      g_CurrentESP.worldPosition = playerWorldOrigin;
      g_CurrentESP.eyeWorldPosition = playerWorldOrigin;
      g_CurrentESP.eyeWorldPosition.z += playerViewOffset.z;
      g_CurrentESP.playerName = player.PlayerName;
      g_CurrentESP.playerFlags = player.PlayerFlags;
      g_CurrentESP.itemDefinitionIndex = player.ItemDefinitionIndex;
      g_CurrentESP.health = player.health;
      g_CurrentESP.armor = player.armor;
      g_CurrentESP.team = player.team;
      g_CurrentESP.entityId = player.entityId;
      g_CurrentESP.boneArray = player.BoneArray;
      g_CurrentESP.playerSpotted = player.PlayerSpotted;
      g_CurrentESP.boxHeight = screenFeetPos.y - screenHeadPos.y;
      g_CurrentESP.boxHalfWidth = g_CurrentESP.boxHeight * 0.25f;

      // Always render snaplines regardless of culling
      if (Esp::Snapline::enabled) {
        DrawPlayerSnapline();
      }

      // Only render other ESP elements if they are completely on screen
      if (Esp::Health::Bar::enabled) {
        // Register health animation automatically - now with proper value change detection
        HealthAnimations::UpdateHealthAnimation(player.entityId, static_cast<float>(player.health));
        DrawPlayerHealth();
      }

      if (Esp::Armor::Bar::enabled) {
        // Register armor animation automatically - now with proper value change detection
        ArmorAnimations::UpdateArmorAnimation(player.entityId, static_cast<float>(player.armor));
        DrawPlayerArmor();
      }

      if (Esp::Box::enabled) {
        if (Esp::Box::type == 0) {
          DrawPlayerBox();
        } else {
          DrawPlayerCorneredBox();
        }
      }

      if (Esp::Box::Filled::enabled) {
        DrawPlayerFilledBox();
      }

      if (Esp::Info::enabled) {
        DrawPlayerInfo();
      }

      if (Esp::Viewline::enabled) {
        // Note: angEyeAngles offset might need to be added to GameData in the future
        Vector playerEyeAngles = driver::read_memory<Vector>( driverHandle, player.pCSPlayerPawn + Offset::Pawn::angEyeAngles );
        // Store eye angles in global structure
        g_CurrentESP.worldPosition = playerEyeAngles; // Reuse worldPosition for eye angles temporarily
        DrawViewline();
      }

      if (Esp::Skeleton::enabled) {
        DrawPlayerSkeleton();

        if (Esp::Skeleton::Dots::enabled) {
          DrawPlayerJoints();
        }

        if (Esp::Skeleton::Head::enabled) {
          DrawPlayerHead();
        }
      }
    }
  }

  // Render death animations using the new clean system
  DeathAnimations::RenderAll(GameData::getViewMatrix(), localTeam);

  // Render projectiles and grenades
  for (const auto& entity : entityList) {
    if ( entity.className.find( "_projectile" ) == std::string::npos)
      continue;

    // Check if this projectile is permanently banned
    if (g_ProjectileTracking.bannedProjectiles.find(entity.BaseEntity) != g_ProjectileTracking.bannedProjectiles.end()) {
      continue; // Skip banned projectiles forever
    }

    std::string cleanProjectileName = RemoveSuffix( entity.className, "_projectile" );

    // CLEAN: Use GameData for entity position
    Vector projectileOrigin = GameData::getEntityPosition(entity.BaseEntity);

    if ( projectileOrigin.isInvalid() )
      continue;

    // Fill global projectile data
    g_CurrentESP.projectileEntityId = entity.BaseEntity;
    g_CurrentESP.projectileOrigin = projectileOrigin;

    // Process projectile tracking FIRST
    ProcessProjectileTracking();

    // Check if projectile is banned (should not render name/box)
    bool isProjectileBanned = (g_ProjectileTracking.bannedProjectiles.find(entity.BaseEntity) != g_ProjectileTracking.bannedProjectiles.end());

    // If banned, skip name/box rendering but continue for trajectory cleanup
    // Don't use 'continue' here because we still need trajectory processing

    // Regular projectile rendering (name and box) - only if not static
    Vector projectileScreenPos;
    view_matrix_t viewMatrix = GameData::getViewMatrix();
    if (Vector::world_to_screen(viewMatrix, projectileOrigin, projectileScreenPos)) {
      // Fill global projectile data
      g_CurrentESP.projectileScreenPos = projectileScreenPos;
      g_CurrentESP.projectileName = cleanProjectileName;

      if (!isProjectileBanned) {
        if (globals::Projectile::name) {
          DrawProjectileName();
        }
        if (globals::Projectile::box) {
          DrawProjectileBox();
        }
      }
    }
  }

  // Render trajectory lines
  if (globals::Projectile::line) {
    DrawProjectileLines();
  } else {
    CleanupProjectileTrajectories();
  }

  // Clean up tracker for entities that no longer exist
  CleanupProjectileTracking(entityList);

  // Render smoke grenades
  for (const auto& entity : entityList) {
    if ( entity.className.find( "_projectile" ) == std::string::npos && !globals::Projectile::enabled )
      continue;

    std::string cleanProjectileName = RemoveSuffix( entity.className, "_projectile" );
    Vector origin = GameData::getEntityPosition(entity.BaseEntity);

    if ( origin.isInvalid() )
      continue;

    bool bSmokeEffectSpawned = driver::read_memory<bool>( driverHandle, entity.BaseEntity + Offset::SmokeGrenadeProjectile::bSmokeEffectSpawned );

    // Fill global smoke data
    g_CurrentESP.smokeEntityId = entity.BaseEntity;
    g_CurrentESP.smokeEffectSpawned = bSmokeEffectSpawned;

    // Process smoke tracking
    ProcessSmokeTracking();

    if ( bSmokeEffectSpawned && entity.className.find( "smokegrenade_projectile" ) != std::string::npos) {
      if ( globals::Smoke::enabled) {
        // Get the smoke's 3D position and project it to screen space
        Vector smokePos = origin;
        smokePos.z += 50.0f;

        Vector screenPos;
        view_matrix_t viewMatrix = GameData::getViewMatrix();
        if (Vector::world_to_screen( viewMatrix, smokePos, screenPos )) {
          // Fill global smoke data
          g_CurrentESP.smokeScreenPos = screenPos;
          g_CurrentESP.smokeOrigin = origin;
          g_CurrentESP.smokeEntityId = entity.BaseEntity;

          if (globals::Smoke::name::enabled) {
            DrawSmokeName();
          }
          if (globals::Smoke::circle::enabled) {
            DrawSmokeCircle();
          }
          if (globals::Smoke::countdown::enabled) {
            DrawSmokeCountdown();
          }
        }
      }
    }
  }

  // Clean up smoke tracking
  CleanupSmokeTracking(entityList);
}

void VISUALS::DrawAimbotCircle() {
  if (globals::Legitbot::Circle::Filled::enabled)
    Render::AADot(centerX + 0.5f, centerY + 0.5f, globals::Legitbot::radius - 1.f, globals::Legitbot::Circle::Filled::Color);
 
  if (globals::Legitbot::Circle::enabled)
    Render::AACircle(centerX + 0.5f, centerY + 0.5f, globals::Legitbot::radius, globals::Legitbot::Circle::Color, 1.f);
}

void VISUALS::DrawPlayerBox() {
  const float boxLeftEdgeX  = g_CurrentESP.screenHead.x - g_CurrentESP.boxHalfWidth;
  const float boxTopEdgeY   = g_CurrentESP.screenHead.y;
  const float boxTotalVisualWidth = g_CurrentESP.boxHalfWidth * 2.0f;

  const ImVec4 mainBoxColor   = Esp::Box::Spotted::drawingColor;
  ImVec4 outlineBoxColor = {0.f, 0.f, 0.f, 1.f};
  // Make outline fade with the main box color
  outlineBoxColor.w = mainBoxColor.w;

  const float outlineThicknessPx = 1.0f;

  if (Esp::Box::outline) {
    const float outerOutlineX = boxLeftEdgeX - outlineThicknessPx;
    const float outerOutlineY = boxTopEdgeY - outlineThicknessPx;
    const float outerOutlineWidth = boxTotalVisualWidth + (outlineThicknessPx * 2.0f);
    const float outerOutlineHeight = g_CurrentESP.boxHeight + (outlineThicknessPx * 2.0f);
    Render::DrawRect(outerOutlineX, outerOutlineY, outerOutlineWidth, outerOutlineHeight, outlineBoxColor, Esp::Box::rounding, 1.f);

    const float innerOutlineX = boxLeftEdgeX + outlineThicknessPx;
    const float innerOutlineY = boxTopEdgeY + outlineThicknessPx;
    const float innerOutlineWidth = boxTotalVisualWidth - (outlineThicknessPx * 2.0f);
    const float innerOutlineHeight = g_CurrentESP.boxHeight - (outlineThicknessPx * 2.0f);
    Render::DrawRect(innerOutlineX, innerOutlineY, innerOutlineWidth, innerOutlineHeight, outlineBoxColor, Esp::Box::rounding, 1.f);
  }

  Render::DrawRect(boxLeftEdgeX, boxTopEdgeY, boxTotalVisualWidth, g_CurrentESP.boxHeight, mainBoxColor, Esp::Box::rounding, 1.f);
}

void VISUALS::DrawPlayerFilledBox() {
  const float boxLeftEdgeX  = g_CurrentESP.screenHead.x - g_CurrentESP.boxHalfWidth;
  const float boxTopEdgeY   = g_CurrentESP.screenHead.y;
  const float boxTotalVisualWidth = g_CurrentESP.boxHalfWidth * 2.0f;

  const ImVec4 topFillColor    = Esp::Box::Filled::Color;
  const ImVec4 bottomFillColor = Esp::Box::Filled::Color2;

  Render::DrawRectFilledMultiColor(
    boxLeftEdgeX,
    boxTopEdgeY,
    boxTotalVisualWidth,
    g_CurrentESP.boxHeight,
    topFillColor,
    bottomFillColor
  );
}

void VISUALS::DrawPlayerCorneredBox() {
  const float cornerLineLength    = g_CurrentESP.boxHalfWidth * Esp::Box::length;

  const float leftEdgeX   = g_CurrentESP.screenHead.x - g_CurrentESP.boxHalfWidth;
  const float rightEdgeX  = g_CurrentESP.screenHead.x + g_CurrentESP.boxHalfWidth;
  const float topEdgeY    = g_CurrentESP.screenHead.y;
  const float bottomEdgeY = g_CurrentESP.screenHead.y + g_CurrentESP.boxHeight;

  const ImVec4 mainCornerColor  = Esp::Box::Spotted::drawingColor;
  ImVec4 outlineCornerColor = {0.f, 0.f, 0.f, 1.f};
  // Make outline fade with the main corner color
  outlineCornerColor.w = mainCornerColor.w;

  const int fixedOutlineOffsets[][2] = {
    {-1, -1}, {1, -1}, {-1, 1}, {1, 1}
  };
  const float outlineLineThickness = 1.f;

  if (Esp::Box::outline) {
    for (int i = 0; i < 4; i++) {
      const float dx = static_cast<float>(fixedOutlineOffsets[i][0]);
      const float dy = static_cast<float>(fixedOutlineOffsets[i][1]);

      Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX - cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX - cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);
    }
  }

  const float mainLineThickness = 1.f;
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX + cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, topEdgeY, rightEdgeX - cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, topEdgeY, rightEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX + cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX - cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

}

void VISUALS::DrawPlayerHealth() {
  if (Esp::Health::Bar::Style::type == Esp::HealthBarStyle::Solid) {
    DrawPlayerHealthBarSolid();
  } else {
    DrawPlayerHealthBarReactive();
  }
}

void VISUALS::DrawPlayerHealthBarReactive() {
  const float animatedHealth = HealthAnimations::GetAnimatedHealth(g_CurrentESP.entityId, static_cast<float>(g_CurrentESP.health));
  // FIXED: Wrapped std::min and std::max in parentheses to avoid macro conflicts
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = g_CurrentESP.boxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (g_CurrentESP.screenHead.x - g_CurrentESP.boxHalfWidth) - barLeftOffset;
  const float barPositionY = g_CurrentESP.screenHead.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  // For reactive health bar, check if this is a death animation using new AnimationManager
  const bool isDeathAnimation = AnimationManager::HasActiveDeathAnimation(g_CurrentESP.entityId);

  ImVec4 currentBarReactiveColor;
  if (isDeathAnimation) {
    // Use death color with fade alpha for death animations
    currentBarReactiveColor = globals::Esp::Death::Color;
    currentBarReactiveColor.w *= AnimationManager::GetDeathFadeAlpha(g_CurrentESP.entityId);
  } else {
    // Normal reactive color calculation
    currentBarReactiveColor = ImVec4{
      1.0f - healthRatioClamped,
      healthRatioClamped,
      0.0f,
      1.0f  // Normal reactive bar should always be fully opaque
    };
  }
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the health bar
  barBackgroundColor.w = currentBarReactiveColor.w;

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      currentBarReactiveColor.x,
      currentBarReactiveColor.y,
      currentBarReactiveColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      currentBarReactiveColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, currentBarReactiveColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    ImVec4 healthTextColor = Esp::Health::Value::Color;
    // Make health text fade but keep it white
    healthTextColor.w = currentBarReactiveColor.w;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerHealthBarSolid() {
  const float animatedHealth = HealthAnimations::GetAnimatedHealth(g_CurrentESP.entityId, static_cast<float>(g_CurrentESP.health));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = g_CurrentESP.boxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (g_CurrentESP.screenHead.x - g_CurrentESP.boxHalfWidth) - barLeftOffset;
  const float barPositionY = g_CurrentESP.screenHead.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  const ImVec4 solidBarColor = Esp::Health::Bar::Color;
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the solid health bar
  barBackgroundColor.w = solidBarColor.w;

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      solidBarColor.x,
      solidBarColor.y,
      solidBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      solidBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, solidBarColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    ImVec4 healthTextColor = Esp::Health::Value::Color;
    // Make health text fade but keep it white
    healthTextColor.w = solidBarColor.w;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerArmor() {
  const float animatedArmor = ArmorAnimations::GetAnimatedArmor(g_CurrentESP.entityId, static_cast<float>(g_CurrentESP.armor));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float armorRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedArmor / 100.f));

  const float barFullVisualWidth = g_CurrentESP.boxHalfWidth * 2.f;
  const float filledBarActualWidth = barFullVisualWidth * armorRatioClamped;
  const float barStartX = g_CurrentESP.screenHead.x - g_CurrentESP.boxHalfWidth;
  const float barTopOffset = 4.f;
  const float barPositionY = g_CurrentESP.screenHead.y + g_CurrentESP.boxHeight + barTopOffset;
  const float barVisualHeight = 2.f;
  const float backgroundVisualHeight = 4.f;
  const float backgroundBorderOffsetY = 1.f;

  const std::string armorValueText = std::to_string(static_cast<int>(animatedArmor));

  const ImVec4 armorBarColor = Esp::Armor::Bar::Color;
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the armor bar
  barBackgroundColor.w = armorBarColor.w;

  if (Esp::Armor::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      armorBarColor.x,
      armorBarColor.y,
      armorBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barStartX,
      barPositionY,
      barFullVisualWidth,
      barVisualHeight,
      armorBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barStartX;
  const float bgRectY = barPositionY - backgroundBorderOffsetY;
  const float bgRectWidth = barFullVisualWidth + 2.f;
  const float bgRectHeight = backgroundVisualHeight;
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderX = barStartX + 1.f;
  Render::DrawRectFilled(filledBarRenderX, barPositionY, filledBarActualWidth, barVisualHeight, armorBarColor, 0);

  if (Esp::Armor::Value::enabled && animatedArmor < 100) {
    ImGui::PushFont(espfont);
    const float armorTextHorizontalOffset = 3.f;
    const float textRenderPosX = barStartX + filledBarActualWidth - armorTextHorizontalOffset;
    const float textRenderPosY = g_CurrentESP.screenHead.y + g_CurrentESP.boxHeight + 2.f;
    ImVec4 armorTextColor = Esp::Armor::Value::Color;
    // Make armor text fade but keep it white
    armorTextColor.w = armorBarColor.w;

    Render::Text(textRenderPosX, textRenderPosY, armorTextColor, armorValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerInfo() {
  const char* cstrItemName = GetItemName(g_CurrentESP.itemDefinitionIndex);
  const std::string currentItemName = (cstrItemName == nullptr) ? "N/A" : cstrItemName;
  const std::string weaponIconGlyph = GunIcon(cstrItemName);

  constexpr uint32_t IN_AIR_FLAG_VALUE = (1 << 0);
  const bool isPlayerGrounded = !(g_CurrentESP.playerFlags & IN_AIR_FLAG_VALUE);
  const std::string playerAirGroundState = isPlayerGrounded ? "InAir" : "OnGround";

  // Dynamic positioning system - elements move up when others are disabled
  const float rightSideBaseX = (g_CurrentESP.screenHead.x + g_CurrentESP.boxHalfWidth) + 3.0f;
  const float rightSideBaseY = g_CurrentESP.screenHead.y;

  // Calculate dynamic Y positions for right-side elements (name, state)
  float currentRightSideY = rightSideBaseY;

  const float nameTextPosX = rightSideBaseX;
  const float nameTextPosY = currentRightSideY;
  if(globals::Esp::Info::Name::player) {
    currentRightSideY += 10.0f; // Move down for next element
  }

  const float stateTextPosX = rightSideBaseX;
  const float stateTextPosY = currentRightSideY;
  if(globals::Esp::Info::state) {
    currentRightSideY += 10.0f; // Move down for next element
  }

  // Calculate dynamic Y positions for bottom elements (weapon text, weapon icon)
  const float bottomBaseY = g_CurrentESP.screenHead.y + g_CurrentESP.boxHeight;
  float currentBottomY = bottomBaseY;

  // Check if armor bar is enabled to add spacing
  if(globals::Esp::Armor::Bar::enabled) {
    currentBottomY += 9.0f; // Add armor bar spacing
  } else {
    currentBottomY += 4.0f; // Add minimum spacing when armor bar is disabled
  }

  ImGui::PushFont(espfont);
  const float itemNameTextWidth = ImGui::CalcTextSize(currentItemName.c_str()).x;
  ImGui::PopFont();
  const float itemNameTextBaseX = g_CurrentESP.screenHead.x;
  const float itemNameTextPosX = itemNameTextBaseX - (itemNameTextWidth / 2.0f);
  const float itemNameTextPosY = currentBottomY;
  if(globals::Esp::Info::Name::weapon) {
    currentBottomY += 10.0f; // Move down for weapon icon
  }

  ImGui::PushFont(gunIcons);
  const float weaponIconGlyphWidth = ImGui::CalcTextSize(weaponIconGlyph.c_str()).x;
  ImGui::PopFont();
  const float weaponIconBaseX = g_CurrentESP.screenHead.x;
  const float weaponIconPosX = weaponIconBaseX - (weaponIconGlyphWidth / 2.0f);
  const float weaponIconPosY = currentBottomY;

  const ImVec4 infoElementColor = Esp::Info::Spotted::drawingColor;
  const float defaultFontSize = fontSize;
  const float iconDisplayFontSize = 18.0f;
  const font_flags_t textRenderStyle = font_flags_t::outline;

  ImGui::PushFont(espfont);
  if(globals::Esp::Info::Name::player){
    Render::Text(nameTextPosX, nameTextPosY, infoElementColor, g_CurrentESP.playerName, defaultFontSize, textRenderStyle);
  }
  if(globals::Esp::Info::state){
    Render::Text(stateTextPosX, stateTextPosY, infoElementColor, playerAirGroundState, defaultFontSize, textRenderStyle);
  }
  if (globals::Esp::Info::Name::weapon){
    Render::Text(itemNameTextPosX, itemNameTextPosY, infoElementColor, currentItemName, defaultFontSize, textRenderStyle);
  }
  ImGui::PopFont();

  ImGui::PushFont(gunIcons);
  if(globals::Esp::Info::Icon::enabled){
    Render::Gun(weaponIconPosX, weaponIconPosY, infoElementColor, weaponIconGlyph, iconDisplayFontSize, textRenderStyle);
  }
  ImGui::PopFont();
}

void VISUALS::DrawViewline() {
  const float viewLineRenderLength = Esp::Viewline::length;
  const float viewLineStartOffset = 10.f;
  const float lineRenderThickness = 1.0f;
  const ImVec4 viewLineColor = Esp::Viewline::Spotted::drawingColor;

  const float dotSquareHalfDim = 1.5f;
  const float dotLineRenderThickness = 1.0f;
  const ImVec4 dotSquareColor = Esp::Viewline::FacingBox::Color;

  const float pitchInRadians = g_CurrentESP.worldPosition.x * 3.14159265359f / 180.f;
  const float yawInRadians = g_CurrentESP.worldPosition.y * 3.14159265359f / 180.f;

  const Vector directionVector = {
    cosf(yawInRadians) * cosf(pitchInRadians),
    sinf(yawInRadians) * cosf(pitchInRadians),
    -sinf(pitchInRadians)
  };

  // FIXED: Removed const from lineWorldStartPos and lineWorldEndPos
  Vector lineWorldStartPos = g_CurrentESP.eyeWorldPosition + directionVector * viewLineStartOffset;
  Vector lineWorldEndPos = g_CurrentESP.eyeWorldPosition + directionVector * (viewLineStartOffset + viewLineRenderLength);

  Vector lineScreenStartPos, lineScreenEndPos;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, lineWorldStartPos, lineScreenStartPos) &&
    Vector::world_to_screen(viewMatrix, lineWorldEndPos, lineScreenEndPos)) {

    Render::AALine(lineScreenStartPos.x, lineScreenStartPos.y, lineScreenEndPos.x, lineScreenEndPos.y, viewLineColor, lineRenderThickness);

    if (Esp::Viewline::FacingBox::enabled) {
      // FIXED: Reverted to original-style calculation for right/up vectors for the dot, avoiding normalize/cross
      Vector dotOrientationRight = {
        -sinf(yawInRadians),
        cosf(yawInRadians),
        0.f
      };
      Vector dotOrientationUp = {
        cosf(yawInRadians) * sinf(pitchInRadians),
        sinf(yawInRadians) * sinf(pitchInRadians),
        cosf(pitchInRadians)
      };

      Vector worldCornerFTR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFTL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;

      Vector screenCornerFTR, screenCornerFTL, screenCornerFBR, screenCornerFBL;
      const bool areDotPointsOnScreen =
        Vector::world_to_screen(viewMatrix, worldCornerFTR, screenCornerFTR) &&
        Vector::world_to_screen(viewMatrix, worldCornerFTL, screenCornerFTL) &&
        Vector::world_to_screen(viewMatrix, worldCornerFBR, screenCornerFBR) &&
        Vector::world_to_screen(viewMatrix, worldCornerFBL, screenCornerFBL);

      if (areDotPointsOnScreen &&
        screenCornerFTR.x != 0.f && screenCornerFTR.y != 0.f && screenCornerFTR.z != 0.f && // Comparing floats to 0.f
        screenCornerFTL.x != 0.f && screenCornerFTL.y != 0.f && screenCornerFTL.z != 0.f &&
        screenCornerFBR.x != 0.f && screenCornerFBR.y != 0.f && screenCornerFBR.z != 0.f &&
        screenCornerFBL.x != 0.f && screenCornerFBL.y != 0.f && screenCornerFBL.z != 0.f) {

        Render::AALine(screenCornerFTL.x, screenCornerFTL.y, screenCornerFTR.x, screenCornerFTR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFTR.x, screenCornerFTR.y, screenCornerFBR.x, screenCornerFBR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBR.x, screenCornerFBR.y, screenCornerFBL.x, screenCornerFBL.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBL.x, screenCornerFBL.y, screenCornerFTL.x, screenCornerFTL.y, dotSquareColor, dotLineRenderThickness);
      }
    }
  }
}

void VISUALS::DrawPlayerSkeleton() {
  auto* gameDriver = GameVars::getInstance()->getDriver();

  const ImVec4 skeletonLineColor = Esp::Skeleton::Spotted::drawingColor;
  const float skeletonLineThickness = 1.0f;
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  constexpr size_t numBoneConnections = sizeof(bConnections) / sizeof(bConnections[0]);
  std::vector<Vector> screenBone1Positions;
  std::vector<Vector> screenBone2Positions;
  screenBone1Positions.reserve(numBoneConnections);
  screenBone2Positions.reserve(numBoneConnections);

  for (const auto& bonePairIndices : bConnections) {
    const uintptr_t bone1MemoryAddress = g_CurrentESP.boneArray + static_cast<uintptr_t>(bonePairIndices.bone1) * 32;
    const uintptr_t bone2MemoryAddress = g_CurrentESP.boneArray + static_cast<uintptr_t>(bonePairIndices.bone2) * 32;

    Vector worldBone1Pos = driver::read_memory<Vector>(gameDriver, bone1MemoryAddress);
    Vector worldBone2Pos = driver::read_memory<Vector>(gameDriver, bone2MemoryAddress);

    Vector boneSegmentVector = worldBone1Pos - worldBone2Pos;
    float boneLength = sqrtf(
      powf(boneSegmentVector.x, 2.f) +
      powf(boneSegmentVector.y, 2.f) +
      powf(boneSegmentVector.z, 2.f)
    );

    Vector screenBone1Pos, screenBone2Pos;
    if (boneLength < MAX_BONE_LENGTH &&
      boneLength > 0.0f &&
      Vector::world_to_screen(viewMatrix, worldBone1Pos, screenBone1Pos) &&
      Vector::world_to_screen(viewMatrix, worldBone2Pos, screenBone2Pos)) {

      screenBone1Positions.push_back(screenBone1Pos);
      screenBone2Positions.push_back(screenBone2Pos);
    }
  }

  for (size_t i = 0; i < screenBone1Positions.size(); ++i) {
    const Vector& p1OnScreen = screenBone1Positions[i];
    const Vector& p2OnScreen = screenBone2Positions[i];
    Render::AALine(p1OnScreen.x, p1OnScreen.y, p2OnScreen.x, p2OnScreen.y, skeletonLineColor, skeletonLineThickness);
  }
}

void VISUALS::DrawPlayerJoints() {
  auto* gameDriver = GameVars::getInstance()->getDriver();
  ImVec4 jointDotColor = Esp::Skeleton::Dots::Color;
  float configuredRadiusFactor = Esp::Skeleton::Dots::radius;
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  float dynamicBaseRadius = 0.0f; // Entspricht dem initialen Radius, wenn Neck.y/Spine.y = 0

  std::vector<Vector> screenJointPositions;
  screenJointPositions.reserve(sizeof(bPoints) / sizeof(bPoints[0]));

  // Phase 1: Sammle Gelenk-Bildschirmpositionen und aktualisiere den dynamicBaseRadius.
  // dynamicBaseRadius wird durch die letzte erfolgreiche Nacken/Wirbels�ulen-Transformation bestimmt.
  for (const auto& jointPointDef : bPoints) {
    uintptr_t boneAddr = g_CurrentESP.boneArray + static_cast<uintptr_t>(jointPointDef.bone) * 32;
    Vector worldJointPos = driver::read_memory<Vector>(gameDriver, boneAddr);
    Vector screenJointPos;

    if (Vector::world_to_screen(viewMatrix, worldJointPos, screenJointPos)) {
      screenJointPositions.push_back(screenJointPos);
    }

    // Aktualisiere dynamicBaseRadius basierend auf der aktuellen Nacken/Wirbels�ulen-Transformation
    Vector worldNeck = driver::read_memory<Vector>(gameDriver, g_CurrentESP.boneArray + bones::neck * 32);
    Vector worldSpine = driver::read_memory<Vector>(gameDriver, g_CurrentESP.boneArray + bones::spine * 32);
    Vector currentScreenNeck, currentScreenSpine;

    if (Vector::world_to_screen(viewMatrix, worldNeck, currentScreenNeck) &&
      Vector::world_to_screen(viewMatrix, worldSpine, currentScreenSpine)) {
      // Nur wenn BEIDE erfolgreich sind, wird der Radius aktualisiert.
      // Dies entspricht der originalen Logik mit den 'continue'-Spr�ngen.
      dynamicBaseRadius = std::abs(currentScreenNeck.y - currentScreenSpine.y);
    }
    // Wenn die Transformation von Nacken/Wirbels�ule fehlschl�gt, beh�lt dynamicBaseRadius
    // seinen Wert aus einer vorherigen erfolgreichen Iteration oder den Initialwert (0.0f).
  }

  // Phase 2: Zeichne alle Gelenkpunkte mit dem final bestimmten dynamicBaseRadius.
  for (const auto& screenPos : screenJointPositions) {
    Render::AADot(screenPos.x, screenPos.y, dynamicBaseRadius * configuredRadiusFactor, jointDotColor);
    if (configuredRadiusFactor <= 0.4f) { // Originale Bedingung f�r den zweiten Punkt
      Render::AADot(screenPos.x, screenPos.y, 0.5f, jointDotColor);
    }
  }
}

void VISUALS::DrawPlayerHead() {
  auto* gameDriver = GameVars::getInstance()->getDriver();

  const uintptr_t headBoneAddr  = g_CurrentESP.boneArray + static_cast<uintptr_t>(bones::head) * 32;
  const uintptr_t neckBoneAddr  = g_CurrentESP.boneArray + static_cast<uintptr_t>(bones::neck) * 32;
  const uintptr_t spineBoneAddr = g_CurrentESP.boneArray + static_cast<uintptr_t>(bones::spine) * 32;

  Vector worldHeadPos  = driver::read_memory<Vector>(gameDriver, headBoneAddr);
  Vector worldNeckPos  = driver::read_memory<Vector>(gameDriver, neckBoneAddr);
  Vector worldSpinePos = driver::read_memory<Vector>(gameDriver, spineBoneAddr);

  Vector screenHead, screenNeck, screenSpine;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, worldHeadPos, screenHead) &&
    Vector::world_to_screen(viewMatrix, worldNeckPos, screenNeck) &&
    Vector::world_to_screen(viewMatrix, worldSpinePos, screenSpine)) {

    const float headCircleRadius = std::abs(screenNeck.y - screenSpine.y);

    const ImVec4 headCircleColor = Esp::Skeleton::Head::Color;
    const float circleLineThickness = 1.0f;

    Render::AACircle(screenHead.x, screenHead.y, headCircleRadius, headCircleColor, circleLineThickness);
  }
}

void VISUALS::DrawPlayerSnapline() {
  const int systemScreenWidth = GetSystemMetrics(SM_CXSCREEN);

  const float lineStartXCoord = static_cast<float>(systemScreenWidth / 2);
  const float lineStartYCoord = 0.f;

  const float lineEndXCoord = g_CurrentESP.screenHead.x;
  const float lineEndYCoord = g_CurrentESP.screenHead.y;

  const ImVec4 snaplineColor = Esp::Snapline::Spotted::drawingColor;
  const float snaplineThickness = Esp::Snapline::thickness;

  Render::AALine(lineStartXCoord, lineStartYCoord, lineEndXCoord, lineEndYCoord, snaplineColor, snaplineThickness);
}





void VISUALS::ProcessProjectileTracking() {
  auto& tracker = g_ProjectileTracking.positionTracker[g_CurrentESP.projectileEntityId];
  auto& trajectory = g_ProjectileTracking.trajectories[g_CurrentESP.projectileEntityId];

  // Check if projectile is at same position (simple comparison)
  bool samePosition = (tracker.first.x == g_CurrentESP.projectileOrigin.x &&
                       tracker.first.y == g_CurrentESP.projectileOrigin.y &&
                       tracker.first.z == g_CurrentESP.projectileOrigin.z);

  if (samePosition) {
    // Projectile is at same position, increment counter
    tracker.second++;

    // If projectile has been static for too long, ban it permanently
    if (tracker.second >= ProjectileTracking::MAX_STATIC_COUNT) {
      g_ProjectileTracking.bannedProjectiles.insert(g_CurrentESP.projectileEntityId);
      g_ProjectileTracking.positionTracker.erase(g_CurrentESP.projectileEntityId); // Remove from tracker

      // Handle trajectory removal - ALWAYS start erase animation when banned
      if (globals::Projectile::line) {
        // Start trajectory removal timer for smooth erase animation
        g_ProjectileTracking.trajectoryStartTime[g_CurrentESP.projectileEntityId] = std::chrono::steady_clock::now();
      } else {
        // Instantly remove trajectory when line rendering is disabled
        auto trajectoryIt = g_ProjectileTracking.trajectories.find(g_CurrentESP.projectileEntityId);
        if (trajectoryIt != g_ProjectileTracking.trajectories.end()) {
          trajectoryIt->second.clear();
        }
      }
    }
  } else {
    // Projectile moved, reset counter and update position
    tracker.first = g_CurrentESP.projectileOrigin;
    tracker.second = 0;

    // Add new position to trajectory only if we have at least 2 positions (to draw a line)
    if (trajectory.empty() ||
        (trajectory.back().x != g_CurrentESP.projectileOrigin.x ||
         trajectory.back().y != g_CurrentESP.projectileOrigin.y ||
         trajectory.back().z != g_CurrentESP.projectileOrigin.z)) {
      trajectory.push_back(g_CurrentESP.projectileOrigin);
    }
  }
}

void VISUALS::DrawProjectileName() {
  if (!globals::Projectile::name) return;

  // Calculate text position above the projectile
  ImGui::PushFont(espfont);
  ImVec2 textSize = ImGui::CalcTextSize(g_CurrentESP.projectileName.c_str());
  ImGui::PopFont();

  float textX = g_CurrentESP.projectileScreenPos.x - (textSize.x / 2.0f);  // Center horizontally
  float textY = g_CurrentESP.projectileScreenPos.y - textSize.y - 5.0f;    // Position above projectile with 5px spacing

  // Draw the projectile name using Render::Text function with ESP font
  ImGui::PushFont(espfont);
  Render::Text(textX, textY, globals::Projectile::Color, g_CurrentESP.projectileName, fontSize, font_flags_t::outline);
  ImGui::PopFont();
}

void VISUALS::DrawProjectileBox() {
  if (!globals::Projectile::box) return;

  float boxSize = 8.0f;
  Render::DrawRect(
    g_CurrentESP.projectileScreenPos.x - boxSize/2,
    g_CurrentESP.projectileScreenPos.y - boxSize/2,
    boxSize,
    boxSize,
    globals::Projectile::Color,
    0.0f,  // rounding
    1.0f   // thickness
  );
}

void VISUALS::DrawProjectileLines() {
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  for (auto trajectoryIt = g_ProjectileTracking.trajectories.begin(); trajectoryIt != g_ProjectileTracking.trajectories.end(); ++trajectoryIt) {
    uintptr_t entityId = trajectoryIt->first;
    auto& trajectory = trajectoryIt->second;

    // Check if this trajectory should be smoothly removed
    auto startTimeIt = g_ProjectileTracking.trajectoryStartTime.find(entityId);
    if (startTimeIt != g_ProjectileTracking.trajectoryStartTime.end()) {
      // Calculate how many points to remove based on time elapsed
      auto now = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTimeIt->second).count();

      // Remove points gradually - one point every 1000ms (slower for testing)
      int pointsToRemove = static_cast<int>(elapsed / 1000);
      if (pointsToRemove > 0 && !trajectory.empty()) {
        int trajectorySize = static_cast<int>(trajectory.size());
        int actualRemove = (pointsToRemove < trajectorySize) ? pointsToRemove : trajectorySize;
        trajectory.erase(trajectory.begin(), trajectory.begin() + actualRemove);

        // If all points removed, clean up completely
        if (trajectory.empty()) {
          g_ProjectileTracking.trajectoryStartTime.erase(startTimeIt);
          continue;
        }
      }
    }

    // Render trajectory as connected lines instead of dots
    if (trajectory.size() >= 2) {
      for (size_t i = 0; i < trajectory.size() - 1; ++i) {
        Vector worldPos1 = trajectory[i];     // Create mutable copy for world_to_screen
        Vector worldPos2 = trajectory[i + 1]; // Create mutable copy for world_to_screen
        Vector screenPos1, screenPos2;

        if (Vector::world_to_screen(viewMatrix, worldPos1, screenPos1) &&
            Vector::world_to_screen(viewMatrix, worldPos2, screenPos2)) {
          // Draw line between consecutive trajectory points
          Render::Line(screenPos1.x, screenPos1.y, screenPos2.x, screenPos2.y, globals::Projectile::Color, 1.f);
        }
      }
    }
  }
}

void VISUALS::CleanupProjectileTrajectories() {
  // If line rendering is disabled, instantly remove all trajectories that have started removal
  for (auto startTimeIt = g_ProjectileTracking.trajectoryStartTime.begin(); startTimeIt != g_ProjectileTracking.trajectoryStartTime.end();) {
    uintptr_t entityId = startTimeIt->first;
    auto trajectoryIt = g_ProjectileTracking.trajectories.find(entityId);
    if (trajectoryIt != g_ProjectileTracking.trajectories.end()) {
      trajectoryIt->second.clear(); // Instantly clear the trajectory
    }
    startTimeIt = g_ProjectileTracking.trajectoryStartTime.erase(startTimeIt);
  }
}

void VISUALS::CleanupProjectileTracking() {
  // Clean up tracker for entities that no longer exist
  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  auto trackerIt = g_ProjectileTracking.positionTracker.begin();
  while (trackerIt != g_ProjectileTracking.positionTracker.end()) {
    bool entityExists = false;
    for (const auto& entity : entityList) {
      if (entity.BaseEntity == trackerIt->first) {
        entityExists = true;
        break;
      }
    }

    if (!entityExists) {
      // Entity disappeared - check if it was banned (static) or just disappeared in air
      bool wasBanned = (g_ProjectileTracking.bannedProjectiles.find(trackerIt->first) != g_ProjectileTracking.bannedProjectiles.end());

      if (wasBanned) {
        // Only erase trajectory if projectile was banned (stood still 100x)
        if (g_ProjectileTracking.trajectoryStartTime.find(trackerIt->first) == g_ProjectileTracking.trajectoryStartTime.end()) {
          if (globals::Projectile::line) {
            // Start trajectory removal timer for smooth erase animation
            g_ProjectileTracking.trajectoryStartTime[trackerIt->first] = std::chrono::steady_clock::now();
          } else {
            // Instantly remove trajectory when line rendering is disabled
            auto trajectoryIt = g_ProjectileTracking.trajectories.find(trackerIt->first);
            if (trajectoryIt != g_ProjectileTracking.trajectories.end()) {
              trajectoryIt->second.clear();
            }
          }
        }
      }
      // If projectile disappeared in air (not banned), keep trajectory visible

      trackerIt = g_ProjectileTracking.positionTracker.erase(trackerIt);
    } else {
      ++trackerIt;
    }
  }

  // Clean up empty trajectories
  auto trajectoryIt = g_ProjectileTracking.trajectories.begin();
  while (trajectoryIt != g_ProjectileTracking.trajectories.end()) {
    if (trajectoryIt->second.empty()) {
      trajectoryIt = g_ProjectileTracking.trajectories.erase(trajectoryIt);
    } else {
      ++trajectoryIt;
    }
  }
}

void VISUALS::ProcessDeathDetection() {
  // Get player list from reader
  auto playerList = reader.getPlayerListCopy();
  int currentlyAlive[64];
  int aliveCount = 0;

  // Collect currently alive players
  for (const auto& player : playerList) {
    if (aliveCount < 64) {
      currentlyAlive[aliveCount] = player.entityId;
      aliveCount++;
    }
  }

  // Check for deaths if death ESP is enabled
  if (globals::Esp::Death::enabled) {
    // Simple death detection - compare with previous frame
    for (int i = 0; i < aliveCount; i++) {
      bool foundInCurrent = false;
      for (int j = 0; j < aliveCount; j++) {
        if (currentlyAlive[j] == currentlyAlive[i]) {
          foundInCurrent = true;
          break;
        }
      }

      if (!foundInCurrent) {
        int entityId = currentlyAlive[i];
        // Player died - find their last known data from current player list
        for (const auto& player : playerList) {
          if (player.entityId == entityId) {
            // CLEAN: Use GameData for player position
            Vector playerWorldOrigin = GameData::getPlayerPosition(player.pCSPlayerPawn);
            Vector playerViewOffset = driver::read_memory<Vector>(g_CurrentESP.driverHandle, player.pCSPlayerPawn + 0xCB0);
            Vector playerEyeWorldPos = playerWorldOrigin;
            playerEyeWorldPos.z += playerViewOffset.z;

            DeathAnimations::RegisterPlayerDeath(
              entityId,
              playerWorldOrigin,
              playerEyeWorldPos,
              player.BoneArray,
              player.PlayerName,
              player.PlayerFlags,
              player.ItemDefinitionIndex,
              player.health,
              player.armor,
              player.team,
              player.PlayerSpotted
            );
            break;
          }
        }
      }
    }
  }

  // Update the known alive set for next frame
  // For now, skip this complex logic - death detection is simplified
}

void VISUALS::ProcessSmokeTracking() {
  auto& smokeState = g_SmokeTracking.smokeStates[g_CurrentESP.smokeEntityId];
  auto& smokeStartTime = smokeState.first;
  auto& smokeActive = smokeState.second;

  if (!smokeActive && g_CurrentESP.smokeEffectSpawned) {
    // Start timing when smoke effect is first detected
    smokeStartTime = std::chrono::steady_clock::now();
    smokeActive = true;
  } else if (!g_CurrentESP.smokeEffectSpawned) {
    // Reset state when smoke effect is no longer active
    smokeActive = false;
  }
}

void VISUALS::DrawSmokeName() {
  if (!globals::Smoke::name::enabled) return;

  ImGui::PushFont(espfont);
  std::string smokeText = "SMOKE";
  float textWidth = ImGui::CalcTextSize(smokeText.c_str()).x;
  float centeredX = g_CurrentESP.smokeScreenPos.x - (textWidth / 2);
  ImGui::PopFont();

  Render::Text(
    centeredX + 3.f,  // 3 = perfect middle
    g_CurrentESP.smokeScreenPos.y - 10,
    globals::Smoke::name::Color,
    smokeText.c_str(),
    fontSize,
    font_flags_t::outline
  );
}

void VISUALS::DrawSmokeCircle() {
  if (!globals::Smoke::circle::enabled) return;

  view_matrix_t viewMatrix = GameData::getViewMatrix();
  HorizontalCircle(g_CurrentESP.smokeOrigin, 150, 64, viewMatrix, globals::Smoke::circle::Color);
}

void VISUALS::DrawSmokeCountdown() {
  if (!globals::Smoke::countdown::enabled) return;

  auto& smokeState = g_SmokeTracking.smokeStates[g_CurrentESP.smokeEntityId];
  auto& smokeStartTime = smokeState.first;
  auto& smokeActive = smokeState.second;

  if (!smokeActive) return;

  // Calculate elapsed time and remaining time
  auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - smokeStartTime).count() / 1000.0f;
  float remainingTimeFloat = (std::max)(0.0f, g_SmokeTracking.maxTime - elapsedTime);
  int remainingTime = static_cast<int>(remainingTimeFloat);

  // Debug: Show actual values
  // Console output: elapsedTime, remainingTimeFloat, maxTime

  if (remainingTimeFloat <= 0.0f) return;

  if (globals::Smoke::countdown::bar::enabled) {
    // Adjust line size with perspective scaling
    float lineWidth = 23.0f;                                    // Full width of the line
    float lineHeight = 1.0f;                                     // Thickness of the line
    float currentWidth = lineWidth * (remainingTimeFloat / g_SmokeTracking.maxTime);  // Line width based on remaining time

    // Line start and end positions in screen space
    float lineStartX = g_CurrentESP.smokeScreenPos.x - (lineWidth / 2);
    float lineStartY = g_CurrentESP.smokeScreenPos.y + 15.0f;  // Offset below the text
    float lineEndX = lineStartX + currentWidth;

    // Render the shadow effect (outline)
    Render::DrawRectFilled(
      lineStartX - 1,    // Extend slightly to the left
      lineStartY - 12,   // Extend slightly upwards
      lineWidth + 2,     // Extend the width
      lineHeight + 3,    // Extend the height
      {0, 0, 0, 255},    // Black color for the background
      0.0f               // No rounding needed
    );

    // Render the main line
    Render::Line(
      lineStartX,                 // Start X position
      lineStartY - 10,            // Start Y position
      lineEndX,                   // End X position
      lineStartY - 10,            // End Y position
      globals::Smoke::countdown::bar::Color,  // Red color for the line
      lineHeight                  // Line thickness
    );

    if (globals::Smoke::countdown::text::enabled) {
      Render::Text(
        lineEndX,
        lineStartY - 13,
        globals::Smoke::countdown::text::Color,
        std::to_string(remainingTime),
        fontSize,
        font_flags_t::outline
      );
    }
  }
}

void VISUALS::CleanupSmokeTracking() {
  // Get player list from reader - need to access reader from global scope
  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  for (auto it = g_SmokeTracking.smokeStates.begin(); it != g_SmokeTracking.smokeStates.end();) {
    bool entityExists = false;
    for (const auto& entity : entityList) {
      if (entity.BaseEntity == it->first) {
        entityExists = true;
        break;
      }
    }

    if (!entityExists) {
      it = g_SmokeTracking.smokeStates.erase(it);
    } else {
      ++it;
    }
  }
}

void VISUALS::Darkmode() {
    Render::DrawRectFilled( 0.f, 0.f, Screen::width, Screen::height, { 0, 0, 0, DarkMode::alpha / 200 }, 0.f );
}

