#pragma once
#include "../../../driver/driver.hpp"
#include "../../../math/vector.hpp" // <PERSON><PERSON><PERSON><PERSON>, dass view_matrix_t hier oder in driver.hpp definiert ist
#include "../../entity.hpp"
#include "../../gamevars.hpp"
#include "../../gamedata.hpp" // GameData convenience wrapper system
#include "../../../render/render.hpp"
#include "../../animations/esp/health_animations.hpp"
#include "../../animations/esp/armor_animations.hpp"
#include "../../animations/esp/death_animations.hpp"
#include "../../bones.hpp"
#include "../../globals.hpp"
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <chrono>

#define MAX_BONE_LENGTH 100.0f  // Maximum bone length for skeleton drawing
inline float fontSize = 9.0f;

// ESP Tracking Structures
struct ProjectileTracking {
  std::unordered_map<uintptr_t, std::pair<Vector, int>> positionTracker;
  std::unordered_set<uintptr_t> bannedProjectiles;
  std::unordered_map<uintptr_t, std::vector<Vector>> trajectories;
  std::unordered_map<uintptr_t, std::chrono::steady_clock::time_point> trajectoryStartTime;
  static const int MAX_STATIC_COUNT = 100;
};

struct SmokeTracking {
  std::unordered_map<uintptr_t, std::pair<std::chrono::steady_clock::time_point, bool>> smokeStates;
  static constexpr float maxTime = 18.0f;
};

struct PlayerTracking {
  std::unordered_set<int> lastKnownAlive;
};

// Current ESP rendering data - updated each frame
struct CurrentESPData {
  // Player data
  Vector screenHead;
  Vector screenFeet;
  Vector worldPosition;
  Vector eyeWorldPosition;
  std::string playerName;
  uint32_t playerFlags;
  uint16_t itemDefinitionIndex;
  int health;
  int armor;
  int team;
  int entityId;
  uint64_t boneArray;
  bool playerSpotted;
  float boxHeight;
  float boxHalfWidth;

  // Projectile data
  Vector projectileScreenPos;
  Vector projectileOrigin;
  std::string projectileName;
  uintptr_t projectileEntityId;

  // Smoke data
  Vector smokeScreenPos;
  Vector smokeOrigin;
  uintptr_t smokeEntityId;
  bool smokeEffectSpawned;

  // View matrix
  view_matrix_t viewMatrix;

  // Driver handle
  HANDLE driverHandle;
};

// Global ESP tracking instances
inline ProjectileTracking g_ProjectileTracking;
inline SmokeTracking g_SmokeTracking;
inline PlayerTracking g_PlayerTracking;
inline CurrentESPData g_CurrentESP;

class VISUALS {
  // Allow DeathAnimations to access private drawing functions
  friend class DeathAnimations;

public:
  static void RenderESP( HANDLE driverHandle, const Reader& reader );

private:
  // Drawing functions are now private again - using global ESP data
  static void DrawPlayerBox();
  static void DrawPlayerCorneredBox();
  static void DrawPlayerInfo();
  static void DrawPlayerSnapline();
  static void DrawPlayerHealth();
  static void DrawPlayerArmor();
  static void DrawPlayerFilledBox();

  static void DrawPlayerHealthBarReactive();
  static void DrawPlayerHealthBarSolid();

  static void DrawViewline();
  static void DrawPlayerSkeleton();
  static void DrawPlayerJoints();
  static void DrawPlayerHead();

  // Death detection function
  static void ProcessDeathDetection();

  // Projectile ESP functions
  static void ProcessProjectileTracking();
  static void DrawProjectileName();
  static void DrawProjectileBox();
  static void DrawProjectileLines();
  static void CleanupProjectileTrajectories();
  static void CleanupProjectileTracking(const std::vector<C_BaseEntity>& entityList);

  // Smoke ESP functions
  static void ProcessSmokeTracking();
  static void DrawSmokeName();
  static void DrawSmokeCircle();
  static void DrawSmokeCountdown();
  static void CleanupSmokeTracking(const std::vector<C_BaseEntity>& entityList);

  static void DrawAimbotCircle();

  static void Darkmode();
};
